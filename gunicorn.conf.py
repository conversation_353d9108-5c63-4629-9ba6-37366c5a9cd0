#!/usr/bin/env python3

import multiprocessing

# Server socket
bind = "127.0.0.1:8002"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 120
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "/var/log/gunicorn/fmcghorecabusiness_access.log"
errorlog = "/var/log/gunicorn/fmcghorecabusiness_error.log"
loglevel = "info"

# Process naming
proc_name = "fmcghorecabusiness"

# Server mechanics
daemon = False
pidfile = "/var/run/gunicorn/fmcghorecabusiness.pid"
user = "www-data"
group = "www-data"
tmp_upload_dir = None

# SSL (we'll handle this with nginx)
# keyfile = None
# certfile = None

# Environment
raw_env = [
    'PYTHONPATH=/usr/lib/python3/dist-packages:/usr/local/lib/python3.12/dist-packages',
]
