from django.shortcuts import render, redirect, get_object_or_404
from datetime import datetime
from blog.models import Blog, Ads, SubscribeNewsletter, EventCount
from core.models import AboutUs, DataCollection
from core.forms import DataCollectionForm
from magazine.models import Flipbook
from django.contrib import messages, auth



def home(request):
    fmcg_insights_banner = Blog.objects.filter(category__name__contains='FMCG INSIGHTS').order_by('-created')[:3]
    hotels_banner = Blog.objects.filter(category__name__contains='HOTELS').order_by('-created')[:3]
    catering_banner = Blog.objects.filter(category__name__contains='CATERING').order_by('-created')[:3]
    restaurant_banner = Blog.objects.filter(category__name__contains='RESTAURANTS & REVIEWS').order_by('-created')[:3]
    equipment_banner = Blog.objects.filter(category__name__contains='EQUIPMENT & SUPPLIES').order_by('-created')[:3]
    fmcg_insights_blog = Blog.objects.filter(category__name__contains='FMCG INSIGHTS').order_by('-created')[:6]
    hotels_blog = Blog.objects.filter(category__name__contains='HOTELS').order_by('-created')[:6]
    restaurant_blog = Blog.objects.filter(category__name__contains='RESTAURANTS & REVIEWS').order_by('-created')[:6]
    events_blog = EventCount.objects.order_by('order_no')[:6]
    equipment_blog = Blog.objects.filter(category__name__contains='EQUIPMENT & SUPPLIES').order_by('-created')[:6]
    catering_blog = Blog.objects.filter(category__name__contains='CATERING').order_by('-created')[:6]
    news_blog = Blog.objects.filter(category__name__contains='NEWS').order_by('-created')[:9]
    ads_section = Ads.objects.all()
    current_month = datetime.today().month

    blog_top = Blog.objects.filter(created__month=current_month).order_by('-created')

    context = {
        'fmcg_insights_banner':fmcg_insights_banner,
        'hotels_banner':hotels_banner,
        'catering_banner':catering_banner,
        'restaurant_banner':restaurant_banner,
        'equipment_banner':equipment_banner,
        'fmcg_insights_blog':fmcg_insights_blog,
        'hotels_blog':hotels_blog,
        'restaurant_blog':restaurant_blog,
        'events_blog':events_blog,
        'news_blog':news_blog,
        'ads_section':ads_section,
        'equipment_blog':equipment_blog,
        'catering_blog':catering_blog,
        'blog_top':blog_top,
    }
    return render(request, 'home.html', context)

def about_us(request):
    about = AboutUs.objects.all()
    return render(request, 'about.html', {'about':about})


def error_404(request, exception):
    return render(request, 'not_found.html')


def maintenance(request):
    return render(request, 'maintenance.html')

def enquiry(request):
    if request.method == 'POST':
        dataform = DataCollectionForm(request.POST)
        if dataform.is_valid():
            dataform.save()
            messages.info(request, 'Thanks For Submit', extra_tags='enquiry')
            return redirect('https://fmcghorecabusiness.com/')
    else:
        dataform = DataCollectionForm()
    return render(request, 'enquiry.html')


def flipbook(request, slug):
    magazine = get_object_or_404(Flipbook, slug=slug)
    return render(request, 'flipbook.html', {'magazine': magazine})
